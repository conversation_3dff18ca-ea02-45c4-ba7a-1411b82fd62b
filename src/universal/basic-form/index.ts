import { INDUSTRY_NEW_TYPE } from './constants'

export const flattenAndFind = (
  array: {
    value: string
    label: string
    children?: { value: string; label: string }[]
  }[],
  targetValue: string,
) => {
  const flattened = array.flatMap((item) =>
    item.children ? [...item.children] : [item],
  )

  return flattened.find((item) => item.value === targetValue)
}

export const getApprovalStatusColor = (item: number) => {
  switch (item) {
    case 2:
      return 'default'
    case 3:
      return 'processing'
    case 4:
      return 'success'
    case 5:
      return 'error'
    default:
      return 'default'
  }
}

export const getIndustryNewTypePath = (targetValue: string) => {
  for (const item of INDUSTRY_NEW_TYPE) {
    if (item.value === targetValue) {
      return [item.value]
    }

    if (item.children && item.children.length > 0) {
      for (const child of item.children) {
        if (child.value === targetValue) {
          return [item.value, child.value]
        }
      }
    }
  }
  return []
}
